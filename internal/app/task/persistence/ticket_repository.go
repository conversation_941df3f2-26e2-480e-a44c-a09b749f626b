package persistence

import (
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/models"
	"assetfindr/internal/app/task/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TicketRepository struct{}

func NewTicketRepository() repository.TicketRepository {
	return &TicketRepository{}
}

func (r *TicketRepository) GetTickets(ctx context.Context, dB database.DBI, tickets *[]models.Ticket, pageSize int, pageNo int, searchKeyword string) (int, error) {
	var totalRecords int64
	query := dB.GetOrm().Model(&models.Ticket{})

	if searchKeyword != "" {
		query = query.Where("LOWER(subject) LIKE LOWER(?)", "%"+searchKeyword+"%")
	}

	if err := query.Count(&totalRecords).Error; err != nil {
		return 0, err
	}

	offset := (pageNo - 1) * pageSize

	if err := query.Offset(offset).Limit(pageSize).Preload("SeverityLevel").Find(tickets).Error; err != nil {
		return 0, err
	}

	return int(totalRecords), nil
}

func (r *TicketRepository) CreateTicket(ctx context.Context, dB database.DBI, ticket *models.Ticket) error {
	if err := dB.GetTx().Create(ticket).Error; err != nil {
		return err
	}
	return nil
}

func (r *TicketRepository) CreateTickets(ctx context.Context, dB database.DBI, tickets []models.Ticket) error {
	return dB.GetTx().Create(&tickets).Error
}

func (r *TicketRepository) GetTicketByID(ctx context.Context, dB database.DBI, id string) (*models.Ticket, error) {
	var ticket models.Ticket
	if err := dB.GetOrm().Model(&models.Ticket{}).
		Joins(`INNER JOIN "tks_TICKET_CATEGORIES" ttc ON "tks_tickets".ticket_category_code = ttc.code`).
		Joins(`INNER JOIN "tks_TICKET_REFERENCE" ttr ON "tks_tickets".ticket_reference_code = ttr.code`).
		Joins(`INNER JOIN "tks_SEVERITY_LEVELS" tsl ON "tks_tickets".severity_level_code = tsl.code`).
		Joins(`INNER JOIN "tks_TICKET_STATUSES" tts ON "tks_tickets".status_code = tts.code`).
		Where("tks_tickets.id = ?", id).
		Preload("TicketCategory").
		Preload("TicketReference").
		Preload("SeverityLevel").
		Preload("TicketStatus").
		First(&ticket).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ticket")
		}
		return nil, err
	}
	return &ticket, nil
}

func (r *TicketRepository) UpdateTicket(ctx context.Context, dB database.DBI, ticket *models.Ticket) error {
	return dB.GetTx().Model(ticket).Clauses(clause.Returning{}).Updates(ticket).Error
}

func (r *TicketRepository) GetTicketList(ctx context.Context, dB database.DBI, req models.GetTicketListParam) (int, []models.Ticket, error) {
	var totalRecords int64
	tickets := []models.Ticket{}
	query := dB.GetOrm().Model(&tickets)

	// if !req.Cond.Where.EndDate.IsZero() {
	// 	req.Cond.Where.EndDate = req.Cond.Where.EndDate.Add(24 * time.Hour)
	// }

	query.Joins(`JOIN "tks_TICKET_CATEGORIES" on "tks_TICKET_CATEGORIES".code = tks_tickets.ticket_category_code`)

	enrichTicketQueryWithWhere(query, req.Cond.Where)

	if req.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(tks_tickets.ticket_number) LIKE LOWER(?)", "%"+req.SearchKeyword+"%").
				Or("LOWER(tks_tickets.subject) LIKE LOWER(?)", "%"+req.SearchKeyword+"%").
				Or(`LOWER("tks_TICKET_CATEGORIES".label) LIKE LOWER(?)`, "%"+req.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), tickets, nil
	}

	enrichTicketQueryWithPreload(query, req.Cond.Preload)

	query.Order("tks_tickets.updated_at DESC")
	offset := (req.PageNo - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Preload("SeverityLevel").Find(&tickets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tickets, nil
}

func enrichTicketQueryWithWhere(query *gorm.DB, where models.TicketWhere) {
	// RequesterUserID:     claim.UserID,
	// 		AssignedToUserID:    claim.UserID,
	if where.ClientID != "" {
		query.Where("tks_tickets.client_id = ?", where.ClientID)
	} // ClientID

	if where.NonTicketAdminCondition != nil {
		newQuery := query.Session(&gorm.Session{NewDB: true}).Unscoped().
			Where("tks_tickets.requester_user_id = ?", where.NonTicketAdminCondition.UserID).
			Or("tks_tickets.assigned_to_user_id = ?", where.NonTicketAdminCondition.UserID)

		if len(where.NonTicketAdminCondition.ReferenceIDs) > 0 {
			newQuery.Or("tks_tickets.reference_id IN ?", where.NonTicketAdminCondition.ReferenceIDs)
		}

		if where.NonTicketAdminCondition.DepartmentID.Valid {
			newQuery.Or("tks_tickets.department_id = ?", where.NonTicketAdminCondition.DepartmentID.String)
		}

		query.Where(newQuery)
	}

	if where.DepartmentID != "" {
		query.Where("tks_tickets.department_id = ?", where.DepartmentID)
	} // DepartmentID

	if len(where.DepartmentIDs) > 0 {
		query.Where("tks_tickets.department_id IN ?", where.DepartmentIDs)
	} // DepartmentIDs

	if len(where.SeverityLevelCodes) > 0 {
		query.Where("tks_tickets.severity_level_code IN ?", where.SeverityLevelCodes)
	} // SeverityLevelCodes

	if len(where.AssignedToUserIDs) > 0 {
		query.Where("tks_tickets.assigned_to_user_id IN ?", where.AssignedToUserIDs)
	} // AssignedToUserIDs

	if len(where.RequestedByUserIDs) > 0 {
		query.Where("tks_tickets.requester_user_id IN ?", where.RequestedByUserIDs)
	} // RequestedByUserIDs

	if len(where.RequesterUserIDs) > 0 {
		query.Where("tks_tickets.requester_user_id IN ?", where.RequesterUserIDs)
	} // RequesterUserIDs

	if len(where.CreatedByUserIDs) > 0 {
		query.Where("tks_tickets.requester_user_id IN ?", where.CreatedByUserIDs)
	} // CreatedByUserIDs

	// DEPRECATED
	// if where.PartnerOwnerID != "" {
	// 	query.Where("tks_tickets.partner_owner_id = ?", where.PartnerOwnerID)
	// } // PartnerOwnerID

	if where.ID != "" {
		query.Where("tks_tickets.id = ?", where.ID)
	} // ID

	/*
		Conflicting where IDs and RequesterOrAssignedUserID
	*/
	if len(where.IDs) > 0 && where.RequesterOrAssignedUserID != "" {
		query.Where("(tks_tickets.id IN ? OR (tks_tickets.requester_user_id = ? OR tks_tickets.assigned_to_user_id = ?))", where.IDs, where.RequesterOrAssignedUserID, where.RequesterOrAssignedUserID)
	} else {
		if len(where.IDs) > 0 {
			query.Where("tks_tickets.id IN ?", where.IDs)
		} // IDs

		if where.RequesterOrAssignedUserID != "" {
			query.Where("tks_tickets.requester_user_id = ? OR tks_tickets.assigned_to_user_id = ?", where.RequesterOrAssignedUserID, where.RequesterOrAssignedUserID)
		} // RequesterAndAssignedUserID
	}

	if where.RequesterAndAssignedUserID != "" {
		query.Where("tks_tickets.requester_user_id = ? AND tks_tickets.assigned_to_user_id = ?", where.RequesterAndAssignedUserID, where.RequesterAndAssignedUserID)
	} // RequesterAndAssignedUserID

	if where.RequesterUserID != "" {
		query.Where("tks_tickets.requester_user_id = ?", where.RequesterUserID)
	} // RequesterUserID

	if where.AssignedUserID != "" {
		query.Where("tks_tickets.assigned_to_user_id = ?", where.AssignedUserID)
	} // AssignedUserID

	if where.TicketReferenceCode != "" {
		query.Where("ticket_reference_code = ?", where.TicketReferenceCode)
	} // TicketReferenceCode

	if where.ReferenceID != "" {
		query.Where("tks_tickets.reference_id = ?", where.ReferenceID)
	} // ReferenceID

	if len(where.ReferenceIDs) > 0 {
		query.Where("tks_tickets.reference_id IN ?", where.ReferenceIDs)
	} // ReferenceIDs

	if len(where.StatusCodes) > 0 {
		query.Where("tks_tickets.status_code IN ?", where.StatusCodes)
	} // StatusCodes

	if len(where.ExcludeStatusCodes) > 0 {
		query.Where("tks_tickets.status_code NOT IN ?", where.ExcludeStatusCodes)
	} // Excluded

	if len(where.Subjects) > 0 {
		query.Where("subject IN ?", where.Subjects)
	} // Subjects

	if len(where.Categories) > 0 {
		query.Where("tks_tickets.ticket_category_code IN ?", where.Categories)
	} // Categories

	if len(where.ExcludeCategories) > 0 {
		query.Where("tks_tickets.ticket_category_code NOT IN ?", where.ExcludeCategories)
	} // Categories

	if !where.StartDate.IsZero() {
		query.Where("tks_tickets.schedule_datetime >= ?", where.StartDate)
	}

	if !where.EndDate.IsZero() {
		query.Where("tks_tickets.schedule_datetime < ?", where.EndDate)
	}

	if !where.StartDueDate.IsZero() {
		query.Where("tks_tickets.due_datetime >= ?", where.StartDueDate)
	}

	if !where.EndDueDate.IsZero() {
		query.Where("tks_tickets.due_datetime <= ?", where.EndDueDate)
	}

	if where.Overdue {
		query.Where("tks_tickets.due_datetime <= ?", time.Now())
	}

	if where.DueSoon {
		query.Where("due_datetime::date != '0001-01-01' AND due_datetime <= ? AND due_datetime >= ?",
			gorm.Expr("current_timestamp + interval '3' day"),
			gorm.Expr("current_timestamp"),
		)
	}

	if where.HasCost {
		query.Where("tks_tickets.cost > 0")
	}

	if where.IsArchived.Valid {
		if where.IsArchived.Bool {
			query.Where("tks_tickets.is_archived IS TRUE")
		} else {
			query.Where("tks_tickets.is_archived IS FALSE")
		}
	}

	if !where.DueDatetime.IsZero() {
		query.Where("timezone('-7', due_datetime)::date = timezone('-7', ?)::date", where.DueDatetime)
	} // DueDatetime

	if !where.ScheduleDatetime.IsZero() {
		query.Where("timezone('-7', schedule_datetime)::date = timezone('-7', ?)::date", where.ScheduleDatetime)
	} // DueDatetime

	if where.HighPriority {
		query.Where("tks_tickets.severity_level_code = ?", constants.TICKET_SEVERITY_HIGH)
	}

	if where.ResolvedIn7Days {
		query.Where("tks_tickets.updated_at - tks_tickets.created_at <= interval '7 days'")
	}

	if where.IsUpcomingServiceReminder {
		now := time.Now()
		endTime := now.AddDate(0, 0, 3)
		query.Where("tks_tickets.schedule_datetime IS NOT NULL AND tks_tickets.schedule_datetime BETWEEN ? AND ?", now, endTime)
	}

	if where.IsOverdueServiceReminder {
		now := time.Now()
		query.Where("tks_tickets.schedule_datetime IS NOT NULL AND tks_tickets.schedule_datetime < ?", now)
	}

	if !where.CreatedBeforeDate.IsZero() {
		query.Where("tks_tickets.updated_at <= ? OR tks_tickets.updated_at IS NULL", where.CreatedBeforeDate)
	}
}

func enrichTicketQueryWithPreload(query *gorm.DB, preload models.TicketPreload) {
	if preload.Parents {
		query.Preload("Parents", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "parent_id", "child_id")
		})
	} // Parents

	if preload.Childs {
		query.Preload("Childs", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "parent_id", "child_id")
		})
	} // Childs

	if preload.SeverityLevel {
		query.Preload("SeverityLevel")
	} // SeverityLevel

	if preload.TicketStatus {
		query.Preload("TicketStatus")
	} // TicketStatus

	if preload.TicketCategory {
		query.Preload("TicketCategory")
	} // TicketCategory
}

func (r *TicketRepository) GetTicket(ctx context.Context, dB database.DBI, condition models.TicketCondition) (*models.Ticket, error) {
	ticket := models.Ticket{}
	query := dB.GetTx().Model(&ticket)
	enrichTicketQueryWithWhere(query, condition.Where)
	enrichTicketQueryWithPreload(query, condition.Preload)

	if len(condition.Columns) > 0 {
		query.Select(condition.Columns)
	}
	err := query.First(&ticket).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ticket")
		}
		return nil, err
	}

	return &ticket, nil
}

func (r *TicketRepository) CountTicket(ctx context.Context, dB database.DBI, condition models.TicketCondition) (int, error) {
	query := dB.GetTx().Model(&models.Ticket{})
	enrichTicketQueryWithWhere(query, condition.Where)

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, err
	}

	return int(total), nil
}

func (r *TicketRepository) GetTicketsV2(ctx context.Context, dB database.DBI, condition models.TicketCondition) ([]models.Ticket, error) {
	tickets := []models.Ticket{}
	query := dB.GetTx().Model(&tickets)
	enrichTicketQueryWithWhere(query, condition.Where)
	enrichTicketQueryWithPreload(query, condition.Preload)

	err := query.Find(&tickets).Error
	if err != nil {
		return nil, err
	}

	return tickets, nil
}

func (r *TicketRepository) GetLatestTicketNumber(ctx context.Context, dB database.DBI, clientID string) (string, error) {
	ticket := models.Ticket{}
	err := dB.GetTx().
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Model(&models.Ticket{}).
		Select("ticket_number").
		Where("client_id = ?", clientID).
		Where("created_at >= date_trunc('day',NOW())").
		Where("created_at < date_trunc('day',NOW()+ INTERVAL '1 day')").
		Where("ticket_number IS NOT NULL").
		Order("created_at DESC").
		Find(&ticket).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", nil
		}

		return "", err
	}

	return ticket.TicketNumber, nil
}

func (ar *TicketRepository) UpdateTicketSeverityLevelCode(ctx context.Context, dB database.DBI, id string, severityLevelCode string) error {
	model := models.Ticket{}
	return dB.GetTx().Model(&model).Where("id = ?", id).Update("severity_level_code", severityLevelCode).Error
}

func (ar *TicketRepository) UpdateTicketSchedule(ctx context.Context, dB database.DBI, id string, scheduleDatetime *time.Time) error {
	model := models.Ticket{}
	return dB.GetTx().Model(&model).Where("id = ?", id).Update("schedule_datetime", scheduleDatetime).Error
}

var ticketContactEditableColumns []string = []string{
	"updated_at",
	"updated_by",
	"name",
	"phone_number",
	"email",
	"role",
	"reference_source_id",
	"reference_source_code",
}

func (ar *TicketRepository) UpsertTicketContact(ctx context.Context, dB database.DBI, ticketContact *models.TicketContact) error {
	return dB.GetTx().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "ticket_id"}, {Name: "type_code"}},
		DoUpdates: clause.AssignmentColumns(ticketContactEditableColumns),
	}).Create(ticketContact).Error
}

func (ar *TicketRepository) UpsertTicketContacts(ctx context.Context, dB database.DBI, ticketContacts []models.TicketContact) error {
	if len(ticketContacts) == 0 {
		return nil
	}

	return dB.GetTx().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "ticket_id"}, {Name: "type_code"}},
		DoUpdates: clause.AssignmentColumns(ticketContactEditableColumns),
	}).Create(&ticketContacts).Error
}

func enrichTicketContactQueryWithWhere(query *gorm.DB, where models.TicketContactWhere) {
	// RequesterUserID:     claim.UserID,
	// 		AssignedToUserID:    claim.UserID,
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.TicketID != "" {
		query.Where("ticket_id = ?", where.TicketID)
	} // TicketID

	if where.TypeCode != "" {
		query.Where("type_code = ?", where.TypeCode)
	} // TypeCode
}

func (r *TicketRepository) GetTicketContacts(ctx context.Context, dB database.DBI, condition models.TicketContactCondition) ([]models.TicketContact, error) {
	ticketContact := []models.TicketContact{}
	query := dB.GetTx().Model(&ticketContact)
	enrichTicketContactQueryWithWhere(query, condition.Where)

	err := query.Find(&ticketContact).Error
	if err != nil {
		return nil, err
	}

	return ticketContact, nil
}

func (r *TicketRepository) UpdateTicketPartnerOwnerName(ctx context.Context, dB database.DBI, partnerID string, newName string) error {
	return dB.GetTx().
		Model(&models.Ticket{}).
		Where("partner_owner_id = ?", partnerID).
		Update("partner_owner_name", newName).Error
}

func enrichTicketStatusQueryWithWhere(query *gorm.DB, where models.TicketStatusWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	}
}

func (r *TicketRepository) GetTicketStatus(ctx context.Context, dB database.DBI, condition models.TicketStatusCondition) (*models.TicketStatus, error) {
	ticket := models.TicketStatus{}
	query := dB.GetTx().Model(&ticket)
	enrichTicketStatusQueryWithWhere(query, condition.Where)

	err := query.First(&ticket).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ticket status")
		}
		return nil, err
	}

	return &ticket, nil
}

func (r *TicketRepository) GetTicketCategoryList(ctx context.Context, dB database.DBI, req models.GetTicketCategoryListParam) (int, []models.TicketCategory, error) {
	var totalRecords int64
	tickets := []models.TicketCategory{}
	query := dB.GetOrm().Model(&tickets)

	enrichTicketCategoryQueryWithWhere(query, req.Cond.Where)

	enrichTicketCategoryQueryWithPreload(query, req.Cond.Preload)

	query.Where("is_hidden IS FALSE")
	if req.SearchKeyword != "" {
		query.Where("LOWER(label) LIKE LOWER(?)", "%"+req.SearchKeyword+"%")
	}

	query.Group("tks_TICKET_CATEGORIES.code")

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), tickets, nil
	}

	offset := (req.PageNo - 1) * req.PageSize
	query.Order("updated_at DESC")
	err = query.Offset(offset).Limit(req.PageSize).Find(&tickets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tickets, nil
}

func (r *TicketRepository) CreateTicketCategory(ctx context.Context, dB database.DBI, category *models.TicketCategory) error {
	return dB.GetTx().Create(category).Error
}

func enrichTicketCategoryQueryWithWhere(query *gorm.DB, where models.TicketCategoryWhere) {
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.ClientIDOrGeneral != "" {
		query.Where("client_id = ? OR client_id = 'GENERAL'", where.ClientIDOrGeneral)
	} // ClientIDOrGeneral

	if where.LowerLabel != "" {
		query.Where("LOWER(label) = LOWER(?)", where.LowerLabel)
	} // LowerLabel

	if where.TicketCategoryId != "" {
		query.Where("code = ?", where.TicketCategoryId)
	} // ticket_categories id

	if where.Code != "" {
		query.Where(`"tks_TICKET_CATEGORIES".code = ?`, where.Code)
	} // Code

	if len(where.StatusCodes) != 0 {
		query.Where(`"tks_TICKET_CATEGORIES".status_code in ?`, where.StatusCodes)
	} // status codes

	if len(where.FilterAssetCategory) > 0 {
		query.Joins(`LEFT JOIN "tks_TICKET_CATEGORY_asset_category_mapping" ttcacm on "tks_TICKET_CATEGORIES".code = ttcacm.category_code`)
		query.Where(`ttcacm.asset_category_code in ? OR ttcacm.asset_category_code is null`, where.FilterAssetCategory)
	}

	if len(where.FilterCustomAssetCategory) > 0 {
		query.Joins(`LEFT JOIN "tks_TICKET_CATEGORY_custom_asset_category_mapping" ttccacm on "tks_TICKET_CATEGORIES".code = ttccacm.category_code`)
		query.Where(`ttccacm.custom_asset_category_id in ? OR ttccacm.custom_asset_category_id is null`, where.FilterCustomAssetCategory)
	}
}

func enrichTicketCategoryQueryWithPreload(query *gorm.DB, preload models.TicketCategoryPreload) {
	if preload.TicketCategoryAssetCategoryMapping {
		query.Preload("AssetCategories")
	} // TicketCategoryAssetCategoryMapping

	if preload.TicketCategoryCustomAssetCategoryMapping {
		query.Preload("CustomAssetCategories")
	} // TicketCategoryCustomAssetCategoryMapping

	if preload.AssetCategory {
		query.Preload("AssetCategories.AssetCategory")
	} // AssetCategory

	if preload.CustomAssetCategory {
		query.Preload("CustomAssetCategories.CustomAssetCategory")
	} // CustomAssetCategory

}

func (r *TicketRepository) GetTicketCategory(ctx context.Context, dB database.DBI, condition models.TicketCategoryCondition) (*models.TicketCategory, error) {
	category := &models.TicketCategory{}
	query := dB.GetOrm().Model(&category)

	enrichTicketCategoryQueryWithWhere(query, condition.Where)
	enrichTicketCategoryQueryWithPreload(query, condition.Preload)

	err := query.First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ticket category")
		}
		return nil, err
	}

	return category, nil
}

func (r *TicketRepository) InsertTicketCategoryAssetCategory(ctx context.Context, dB database.DBI, ticketCategoryAssetCategory *models.TicketCategoryAssetCategoryMapping) error {
	return dB.GetTx().Create(ticketCategoryAssetCategory).Error
}

func (r *TicketRepository) InsertTicketCategoryCustomAssetCategory(ctx context.Context, dB database.DBI, ticketCategoryAssetCategory *models.TicketCategoryCustomAssetCategoryMapping) error {
	return dB.GetTx().Create(ticketCategoryAssetCategory).Error
}

func (r *TicketRepository) UpdateTicketCategoryStatus(ctx context.Context, dB database.DBI, code string, statusCode string) error {
	return dB.GetTx().
		Model(&models.TicketCategory{}).
		Where("code = ?", code).
		Update("status_code", statusCode).Error
}
